import { ChevronDown, Menu, X } from "lucide-react";
import { useMenuState } from "@/hooks/useMenuState";
import { UserMenu } from "./UserMenu";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

interface HeaderProps {
  currentPage?: "home" | "calendar" | "ranking" | "regolamento" | "tools";
}

type NavLinkProps = {
  href: string;
  label: string;
  isActive: boolean;
  onClick?: () => void;
  isMobile?: boolean;
};

/**
 * Componente NavLink riutilizzabile per i link di navigazione
 */
const NavLink = ({ href, label, isActive, onClick, isMobile = false }: NavLinkProps) => {
  return (
    <a
      href={href}
      className={`text-sm font-medium transition-colors duration-200 relative py-2 rounded-md whitespace-nowrap ${
        isMobile ? "px-1" : "px-2 lg:px-4 hover:bg-blue-900/20"
      } ${
        isActive
          ? "text-white bg-blue-800/30"
          : "text-blue-200 hover:text-white"
      }`}
      onClick={onClick}
    >
      {label}
    </a>
  );
};

/**
 * Componente DropdownLink simile a NavLink ma per il dropdown Tools
 */
const DropdownLink = ({ label, isActive, onClick, isMobile = false }: Omit<NavLinkProps, 'href'>) => {
  return (
    <button
      className={`text-sm font-medium transition-colors duration-200 relative py-2 rounded-md ${
        isMobile ? "px-1" : "px-4 hover:bg-blue-900/20"
      } ${
        isActive
          ? "text-white bg-blue-800/30"
          : "text-blue-200 hover:text-white"
      } flex items-center`}
      onClick={onClick}
    >
      {label}
      <ChevronDown size={16} className="ml-1" />
    </button>
  );
};

export function Header({ currentPage }: HeaderProps) {
  const { mobileMenuOpen, toggleMenu, closeMenu } = useMenuState();
  const [toolsDropdownOpen, setToolsDropdownOpen] = useState(false);

  const toggleToolsDropdown = () => {
    setToolsDropdownOpen(!toolsDropdownOpen);
  };

  // Configurazione dei link di navigazione
  const navLinks = [
    { href: "/", label: "Home", id: "home" },
    { href: "/calendar", label: "Calendario", id: "calendar" },
    { href: "/classifica", label: "Classifica", id: "ranking" },
    { href: "/regolamento", label: "Regolamento", id: "regolamento" },
    { 
      href: "https://magic.wizards.com/formats/pauper", 
      label: "Cos'è Pauper?", 
      id: "pauper",
      isExternal: true 
    }
  ];

  // Configurazione dei link tools
  const toolsLinks = [
    { href: "/tools/life-counter", label: "Life Counter", id: "life-counter" },
    { href: "/tools/mana-counter", label: "Mana Counter", id: "mana-counter" }
  ];

  // Indice dopo il quale inserire il dropdown Tools (dopo "Classifica")
  const insertToolsAfterIndex = navLinks.findIndex(link => link.id === "ranking");

  return (
    <header className="sticky top-0 z-50 px-4 sm:px-6 lg:px-8 pt-4">
      <nav className="max-w-7xl xl:max-w-full xl:mx-8 2xl:mx-16 mx-auto bg-black/20 backdrop-blur-md border border-blue-500/30 rounded-2xl shadow-lg" aria-label="Top">
        <div className="flex items-center justify-between h-14 px-4 sm:px-6">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Image 
                src="/logo.png" 
                alt="Lega Pauper Adriatica" 
                width={120} 
                height={40} 
                className="h-10 sm:h-12 w-auto" 
                priority
              />
            </Link>
          </div>

          {/* Mobile menu button - Right side, vertically centered */}
          <div className="sm:hidden flex items-center">
            <button
              type="button"
              className="text-blue-300 hover:text-white p-2 rounded-md hover:bg-blue-900/20 transition-colors duration-200 flex items-center justify-center"
              onClick={toggleMenu}
              aria-label={mobileMenuOpen ? "Chiudi menu" : "Apri menu"}
            >
              {mobileMenuOpen ? <X size={28} /> : <Menu size={28} />}
            </button>
          </div>

          {/* Desktop Navigation - Centered */}
          <div className="hidden sm:flex sm:items-center sm:space-x-1 lg:space-x-2 absolute left-1/2 transform -translate-x-1/2">
            {navLinks.slice(0, insertToolsAfterIndex + 1).map((link) => (
              link.isExternal ? (
                <a
                  key={link.id}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm font-medium transition-colors duration-200 py-2 px-2 lg:px-4 rounded-md text-blue-200 hover:text-white hover:bg-blue-900/20 whitespace-nowrap"
                >
                  {link.label}
                </a>
              ) : (
                <NavLink
                  key={link.id}
                  href={link.href}
                  label={link.label}
                  isActive={currentPage === link.id}
                />
              )
            ))}
            
            {/* Tools Dropdown - Desktop - subito dopo Classifica */}
            <div className="relative">
              <DropdownLink
                label="Tools"
                isActive={currentPage === "tools"}
                onClick={toggleToolsDropdown}
              />
              
              {toolsDropdownOpen && (
                <div className="absolute left-0 mt-1 w-48 bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-lg shadow-lg z-10">
                  <div className="py-1">
                    {toolsLinks.map((tool) => (
                      <a
                        key={tool.id}
                        href={tool.href}
                        className="text-white flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
                        role="menuitem"
                      >
                        {tool.label}
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
            
            {/* Resto dei link dopo Tools */}
            {navLinks.slice(insertToolsAfterIndex + 1).map((link) => (
              link.isExternal ? (
                <a
                  key={link.id}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm font-medium transition-colors duration-200 py-2 px-2 lg:px-4 rounded-md text-blue-200 hover:text-white hover:bg-blue-900/20 whitespace-nowrap"
                >
                  {link.label}
                </a>
              ) : (
                <NavLink
                  key={link.id}
                  href={link.href}
                  label={link.label}
                  isActive={currentPage === link.id}
                />
              )
            ))}
          </div>

          {/* Area Riservata - Desktop - Right side */}
          <div className="hidden sm:flex sm:items-center">
            <UserMenu />
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="sm:hidden py-2 px-4 sm:px-6 border-t border-blue-500/30">
            <div className="flex flex-col space-y-2">
              {navLinks.slice(0, insertToolsAfterIndex + 1).map((link) => (
                link.isExternal ? (
                  <a
                    key={link.id}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm font-medium text-blue-300 hover:text-white transition-colors py-2 px-1"
                    onClick={closeMenu}
                  >
                    {link.label}
                  </a>
                ) : (
                  <NavLink
                    key={link.id}
                    href={link.href}
                    label={link.label}
                    isActive={currentPage === link.id}
                    onClick={closeMenu}
                    isMobile={true}
                  />
                )
              ))}
              
              {/* Tools Dropdown - Mobile - subito dopo Classifica */}
              <div className="relative">
                <DropdownLink
                  label="Tools"
                  isActive={currentPage === "tools"}
                  onClick={toggleToolsDropdown}
                  isMobile={true}
                />
                
                {toolsDropdownOpen && (
                  <div className="mt-1 bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-lg">
                    <div className="py-1">
                      {toolsLinks.map((tool) => (
                        <a
                          key={tool.id}
                          href={tool.href}
                          className="text-white flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
                          onClick={closeMenu}
                        >
                          {tool.label}
                        </a>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              {/* Resto dei link dopo Tools */}
              {navLinks.slice(insertToolsAfterIndex + 1).map((link) => (
                link.isExternal ? (
                  <a
                    key={link.id}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm font-medium text-blue-300 hover:text-white transition-colors py-2 px-1"
                    onClick={closeMenu}
                  >
                    {link.label}
                  </a>
                ) : (
                  <NavLink
                    key={link.id}
                    href={link.href}
                    label={link.label}
                    isActive={currentPage === link.id}
                    onClick={closeMenu}
                    isMobile={true}
                  />
                )
              ))}
              
              {/* Separatore per il menu mobile */}
              <div className="border-t border-blue-500/30 my-2"></div>
              
              {/* Area Riservata - Mobile */}
              <div className="py-1">
                <UserMenu />
              </div>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
} 