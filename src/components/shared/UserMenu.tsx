'use client';

import { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useAuthContext } from '@/lib/providers/AuthProvider';
import { LogOut, ChevronDown, Settings, User } from 'lucide-react';
import Link from 'next/link';
import { useIsAdmin } from '@/hooks/useIsAdmin';
import { getDisplayName } from '@/lib/utils';

export function UserMenu() {
  const { user, player, signOut, loading } = useAuthContext();
  const { isAdmin, loading: adminLoading } = useIsAdmin();
  const [isOpen, setIsOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuPos, setMenuPos] = useState<{ top: number; left: number } | null>(null);
  const [mounted, setMounted] = useState(false);


  // Imposta mounted a true dopo il montaggio del componente
  useEffect(() => {
    setMounted(true);
  }, []);

  // Chiudi il dropdown quando si clicca fuori
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (buttonRef.current && buttonRef.current.contains(target)) return;
      if (menuRef.current && menuRef.current.contains(target)) return;
      setIsOpen(false);
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Aggiorna continuamente la posizione quando il menu è aperto (robusto su scroll/resize/mobile)
  useEffect(() => {
    if (!isOpen) return;

    const updatePosition = () => {
      if (!buttonRef.current) return;
      const r = buttonRef.current.getBoundingClientRect();
      const menuWidth = 192; // w-48
      const gap = 4; // mt-1
      const padding = 8; // margine dai bordi viewport
      const isSmall = window.matchMedia('(max-width: 640px)').matches; // Tailwind sm breakpoint
      const targetLeft = isSmall ? r.left : (r.right - menuWidth);
      const left = Math.max(
        padding,
        Math.min(window.innerWidth - menuWidth - padding, targetLeft)
      );
      const top = r.bottom + gap;
      setMenuPos((prev) => (prev && prev.top === top && prev.left === left) ? prev : { top, left });
    };

    let frame = 0;
    const loop = () => {
      updatePosition();
      frame = requestAnimationFrame(loop);
    };
    frame = requestAnimationFrame(loop);

    // Aggiorna anche su resize/orientation change/visualViewport
    window.addEventListener('resize', updatePosition);
    window.addEventListener('orientationchange', updatePosition);
    window.visualViewport?.addEventListener('resize', updatePosition);
    window.visualViewport?.addEventListener('scroll', updatePosition);

    return () => {
      cancelAnimationFrame(frame);
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('orientationchange', updatePosition);
      window.visualViewport?.removeEventListener('resize', updatePosition);
      window.visualViewport?.removeEventListener('scroll', updatePosition);
    };
  }, [isOpen]);


  // Salva l'URL corrente per il reindirizzamento post-logout
  const handleLogout = async () => {
    if (typeof window !== 'undefined') {
      // Salva l'URL corrente prima del logout
      const currentPath = window.location.pathname + window.location.search;
      localStorage.setItem('logoutReturnUrl', currentPath);
    }
    await signOut();
    setIsOpen(false);
  };

  // Non renderizzare nulla durante il rendering lato server o prima del montaggio
  // Questo previene errori di idratazione
  if (!mounted) {
    return (
      <div className="w-[120px] h-[36px] bg-blue-500/10 border border-blue-500/30 rounded-md"></div>
    );
  }

  // Mostra un placeholder durante il caricamento
  if (loading || adminLoading) {
    return (
      <div className="w-[120px] h-[36px] bg-blue-500/10 border border-blue-500/30 rounded-md"></div>
    );
  }

  // Se l'utente non è autenticato, mostra il pulsante "Area Riservata"
  if (!user) {
    // Salva l'URL corrente per il reindirizzamento post-login
    const handleLoginClick = () => {
      if (typeof window !== 'undefined') {
        // Salva l'URL corrente come returnUrl
        const currentPath = window.location.pathname + window.location.search;
        // Configura il parametro returnUrl per la pagina di login
        sessionStorage.setItem('returnUrl', currentPath);
      }
    };

    return (
      <Link
        href="/login"
        className="text-sm font-medium text-blue-300 hover:text-white transition-colors py-2 px-3 rounded-md border border-blue-500/30 hover:bg-blue-500/10"
        onClick={handleLoginClick}
      >
        Area Riservata
      </Link>
    );
  }

  // Solo se l'utente è autenticato, mostra il menu utente
  return (
    <div className="relative inline-block">
      <button
        ref={buttonRef}
        onClick={() => {
          // Pre-calcola la posizione prima dell'apertura per evitare flash
          const r = buttonRef.current?.getBoundingClientRect();
          if (r) {
            const menuWidth = 192; // w-48
            const gap = 4;
            const padding = 8;
            const isSmall = window.matchMedia('(max-width: 640px)').matches; // Tailwind sm breakpoint
            const targetLeft = isSmall ? r.left : (r.right - menuWidth);
            const left = Math.max(
              padding,
              Math.min(window.innerWidth - menuWidth - padding, targetLeft)
            );
            const top = r.bottom + gap;
            setMenuPos({ top, left });
          }
          setIsOpen((prev) => !prev);
        }}
        className="flex items-center gap-1 px-3 py-1.5 bg-black/20 backdrop-blur-sm border border-blue-500/30 hover:bg-black/30 rounded-lg text-sm font-medium transition-colors"
      >
        {getDisplayName(player, user)}
        <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && menuPos && mounted && createPortal(
        <div
          ref={menuRef}
          className="w-48 bg-black/20 backdrop-blur-sm border border-blue-500/30 rounded-lg shadow-lg z-[1000]"
          style={{ position: 'fixed', top: menuPos.top, left: menuPos.left }}
        >
          <div className="py-1">
            {isAdmin ? (
              <>
                <Link
                  href="/admin/dashboard"
                  className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  <Settings size={16} />
                  Pannello Admin
                </Link>
                <Link
                  href="/profile"
                  className="flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
                  onClick={() => setIsOpen(false)}
                >
                  <User size={16} />
                  Profilo Giocatore
                </Link>
              </>
            ) : (
              <Link
                href="/profile"
                className="text-white flex items-center gap-2 w-full px-4 py-2 text-left text-sm hover:bg-blue-900/50 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <User size={16} />
                Profilo utente
              </Link>
            )}
            <button
              onClick={handleLogout}
              className="text-red-400 flex items-center gap-2 w-full text-left px-4 py-2 text-sm hover:bg-blue-900/50 transition-colors"
            >
              <LogOut size={16} />
              Disconnetti
            </button>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
}
