import React from 'react';
import { NavLink } from './NavLink';
import { ToolsDropdown } from './ToolsDropdown';
import { NAV_LINKS, TOOLS_INSERT_INDEX, CurrentPage } from './constants';
import { UserMenu } from '../UserMenu';

interface DesktopNavigationProps {
  currentPage?: CurrentPage;
}

/**
 * Desktop navigation component with horizontal menu and Tools dropdown
 */
export const DesktopNavigation: React.FC<DesktopNavigationProps> = ({ currentPage }) => {

  return (
    <>
      {/* Desktop Navigation - Centered */}
      <div className="hidden sm:flex sm:items-center sm:space-x-1 lg:space-x-2 absolute left-1/2 transform -translate-x-1/2">
        {/* Links before Tools dropdown */}
        {NAV_LINKS.slice(0, TOOLS_INSERT_INDEX + 1).map((link) => (
          <NavLink
            key={link.id}
            href={link.href}
            label={link.label}
            isActive={currentPage === link.id}
            isExternal={link.isExternal}
          />
        ))}
        
        {/* Tools Dropdown */}
        <ToolsDropdown currentPage={currentPage} />
        
        {/* Links after Tools dropdown */}
        {NAV_LINKS.slice(TOOLS_INSERT_INDEX + 1).map((link) => (
          <NavLink
            key={link.id}
            href={link.href}
            label={link.label}
            isActive={currentPage === link.id}
            isExternal={link.isExternal}
          />
        ))}
      </div>

      {/* Area Riservata - Desktop - Right side */}
      <div className="hidden sm:flex sm:items-center">
        <UserMenu />
      </div>
    </>
  );
};
