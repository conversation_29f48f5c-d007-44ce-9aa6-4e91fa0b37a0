import React, { forwardRef } from 'react';
import { ChevronDown } from 'lucide-react';

export interface DropdownLinkProps {
  label: string;
  isActive: boolean;
  onClick?: () => void;
  isMobile?: boolean;
  isOpen?: boolean;
}

/**
 * Dropdown link component for expandable menu items (e.g., Tools)
 */
export const DropdownLink = forwardRef<HTMLButtonElement, DropdownLinkProps>(({
  label,
  isActive,
  onClick,
  isMobile = false,
  isOpen = false
}, ref) => {
  if (isMobile) {
    // Versione mobile semplificata
    const baseClasses = "text-sm font-medium transition-colors duration-200 relative py-2 rounded-md flex items-center px-1";
    const activeClasses = isActive
      ? "text-white bg-blue-800/30"
      : "text-blue-200 hover:text-white";

    return (
      <button
        ref={ref}
        className={`${baseClasses} ${activeClasses}`}
        onClick={onClick}
        type="button"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {label}
        <ChevronDown size={16} className={`ml-1 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>
    );
  }

  // Versione desktop con stile UserMenu
  return (
    <button
      ref={ref}
      onClick={onClick}
      className="flex items-center gap-1 px-3 py-1.5 bg-black/20 backdrop-blur-sm border border-blue-500/30 hover:bg-black/30 rounded-lg text-sm font-medium transition-colors"
      type="button"
      aria-expanded={isOpen}
      aria-haspopup="true"
    >
      {label}
      <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
    </button>
  );
});

DropdownLink.displayName = 'DropdownLink';
