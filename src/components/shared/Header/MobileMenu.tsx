import React from 'react';
import { NavLink } from './NavLink';
import { ToolsDropdown } from './ToolsDropdown';
import { NAV_LINKS, TOOLS_INSERT_INDEX, CurrentPage } from './constants';
import { UserMenu } from '../UserMenu';

interface MobileMenuProps {
  isOpen: boolean;
  currentPage?: CurrentPage;
  onClose: () => void;
}

/**
 * Mobile menu component with vertical navigation and collapsible Tools section
 */
export const MobileMenu: React.FC<MobileMenuProps> = ({
  isOpen,
  currentPage,
  onClose
}) => {

  if (!isOpen) return null;

  return (
    <div className="sm:hidden py-2 px-4 sm:px-6 border-t border-blue-500/30">
      <div className="flex flex-col space-y-2">
        {/* Links before Tools */}
        {NAV_LINKS.slice(0, TOOLS_INSERT_INDEX + 1).map((link) => (
          <NavLink
            key={link.id}
            href={link.href}
            label={link.label}
            isActive={currentPage === link.id}
            onClick={onClose}
            isMobile={true}
            isExternal={link.isExternal}
          />
        ))}
        
        {/* Tools Dropdown - Mobile */}
        <ToolsDropdown currentPage={currentPage} isMobile={true} onClose={onClose} />
        
        {/* Links after Tools */}
        {NAV_LINKS.slice(TOOLS_INSERT_INDEX + 1).map((link) => (
          <NavLink
            key={link.id}
            href={link.href}
            label={link.label}
            isActive={currentPage === link.id}
            onClick={onClose}
            isMobile={true}
            isExternal={link.isExternal}
          />
        ))}
        
        {/* Separator for mobile menu */}
        <div className="border-t border-blue-500/30 my-2"></div>
        
        {/* Area Riservata - Mobile */}
        <div className="py-1">
          <UserMenu />
        </div>
      </div>
    </div>
  );
};
