"use client";

import { useState } from "react";
import Image from "next/image";

type ManaType = "white" | "blue" | "black" | "red" | "green";

const ManaCounter = () => {
  const [manaCount, setManaCount] = useState<Record<ManaType, number>>({
    white: 0,
    blue: 0,
    black: 0,
    red: 0,
    green: 0,
  });

  const [isFullscreen, setIsFullscreen] = useState(false);

  const handleManaChange = (type: ManaType, amount: number) => {
    setManaCount(prev => ({
      ...prev,
      [type]: Math.max(0, prev[type] + amount),
    }));
  };

  const resetMana = () => {
    setManaCount({
      white: 0,
      blue: 0,
      black: 0,
      red: 0,
      green: 0,
    });
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Configurazione dei simboli di mana e colori con i colori esatti dalla reference
  const manaConfig: Record<ManaType, { bgColor: string; svgPath: string }> = {
    white: {
      bgColor: "#FAFCB3",
      svgPath: "/mana/mana-w.svg"
    },
    blue: {
      bgColor: "#57B2F1",
      svgPath: "/mana/mana-u.svg"
    },
    black: {
      bgColor: "#707070",
      svgPath: "/mana/mana-b.svg"
    },
    red: {
      bgColor: "#F23C44",
      svgPath: "/mana/mana-r.svg"
    },
    green: {
      bgColor: "#25A955",
      svgPath: "/mana/mana-g.svg"
    },
  };

  const manaOrder: ManaType[] = ["white", "blue", "black", "red", "green"];

  // Funzione per gestire i click/tap sulle barre
  const handleBarClick = (type: ManaType, event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const isDesktop = window.innerWidth >= 768; // md breakpoint

    if (isDesktop) {
      // Desktop: click sulla metà superiore incrementa, metà inferiore decrementa
      const clickY = event.clientY - rect.top;
      const isUpperHalf = clickY < rect.height / 2;
      handleManaChange(type, isUpperHalf ? 1 : -1);
    } else {
      // Mobile: click sulla metà destra incrementa, metà sinistra decrementa
      const clickX = event.clientX - rect.left;
      const isRightHalf = clickX > rect.width / 2;
      handleManaChange(type, isRightHalf ? 1 : -1);
    }
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-black' : 'relative'}`}>
      {/* Fullscreen toggle button - solo su mobile */}
      <div className="md:hidden absolute top-4 right-4 z-10">
        <button
          onClick={toggleFullscreen}
          className="bg-black/50 text-white p-2 rounded-full backdrop-blur-sm"
        >
          {isFullscreen ? '✕' : '⛶'}
        </button>
      </div>

      {/* Container principale */}
      <div className={`
        ${isFullscreen ? 'h-screen' : 'h-full min-h-[500px]'}
        flex flex-col md:flex-row
        ${isFullscreen ? 'p-0' : 'p-0'}
        gap-0
      `}>
        {manaOrder.map((type) => (
          <div
            key={type}
            className={`
              flex-1 relative cursor-pointer select-none
              min-h-[80px] md:min-h-[400px]
            `}
            style={{ backgroundColor: manaConfig[type].bgColor }}
            onClick={(e) => handleBarClick(type, e)}
          >
            {/* Contenuto della barra */}
            <div className="absolute inset-0 flex items-center">
              {/* Layout mobile: simbolo a sinistra, counter al centro */}
              <div className="md:hidden w-full flex items-center">
                <div className="ml-6">
                  <Image
                    src={manaConfig[type].svgPath}
                    alt={`${type} mana`}
                    width={isFullscreen ? 64 : 48}
                    height={isFullscreen ? 64 : 48}
                    className="drop-shadow-lg"
                  />
                </div>
                <div className="flex-1 text-center">
                  <div className={`
                    text-black font-bold
                    ${isFullscreen ? 'text-8xl' : 'text-6xl'}
                    drop-shadow-lg
                  `}>
                    {manaCount[type]}
                  </div>
                </div>
              </div>

              {/* Layout desktop: tutto centrato verticalmente */}
              <div className="hidden md:flex w-full h-full flex-col items-center justify-center">
                <div className="mb-4">
                  <Image
                    src={manaConfig[type].svgPath}
                    alt={`${type} mana`}
                    width={48}
                    height={48}
                    className="drop-shadow-lg"
                  />
                </div>
                <div className="text-black font-bold text-4xl drop-shadow-lg">
                  {manaCount[type]}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Reset button - nascosto in fullscreen */}
      {!isFullscreen && (
        <div className="mt-4 flex justify-center">
          <button
            onClick={resetMana}
            className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow-md transition-colors"
          >
            Reset
          </button>
        </div>
      )}
    </div>
  );
};

export default ManaCounter; 