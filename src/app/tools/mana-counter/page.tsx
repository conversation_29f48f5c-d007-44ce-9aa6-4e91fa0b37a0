import { Metadata } from "next";
import ManaCounter from "@/components/tools/ManaCounter";
import { MainLayout } from "@/components/layout/MainLayout";

export const metadata: Metadata = {
  title: "Mana Counter | Lega Pauper Adriatica",
  description: "Contatore di mana per i giocatori di Magic: The Gathering",
};

export default function ManaCounterPage() {
  return (
    <MainLayout currentPage="tools">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl md:text-3xl font-bold text-center mb-6 text-white">
          Mana Counter
        </h1>
        <p className="text-blue-200 text-center mb-8">
          Tieni traccia del mana disponibile nel tuo pool durante le partite di Magic: The Gathering
        </p>
        <div className="bg-black/20 backdrop-blur-sm rounded-lg border border-blue-500/30 overflow-hidden">
          <ManaCounter />
        </div>
      </div>
    </MainLayout>
  );
}